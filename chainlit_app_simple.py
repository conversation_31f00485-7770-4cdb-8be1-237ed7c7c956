"""Chainlit + LangGraph Copilot with Plotly support
====================================================
Version corrigée - Résout les problèmes d'affichage des graphiques,
améliore le streaming et la gestion des erreurs.
"""

###############################################################################
# 0. Standard library imports & environment tweaks
###############################################################################

import os
import sys
import uuid
import json
import pickle
import asyncio
import pprint
from typing import Dict, List, Any, Optional
import traceback
# Make sure Python streams are unbuffered so print() shows up immediately.
# (Works only when the process is launched by `python -u`, but we do our part.)
os.environ.setdefault("PYTHONUNBUFFERED", "1")

###############################################################################
# 1. Third‑party imports  
###############################################################################

from dotenv import load_dotenv

import chainlit as cl
from chainlit.types import ThreadDict
from chainlit.data.sql_alchemy import SQLAlchemyDataLayer

from langchain_core.messages import HumanMessage, ToolMessage, AIMessage, AIMessageChunk
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

# Local modules (your original project structure)
from main_copilot import (
    create_agent,
    make_postgres_checkpointer,  # still used elsewhere
    AgentState,
    InputData,
    _merge,
    ensure_message_ids,
    remove_duplicate_messages,
    get_prompt_for_partner,
)
from sandbox_client import SandboxClient
from plot_template import apply_company_style  # Import the plot styling function

###############################################################################
# 2. Environment & data‑layer initialisation
###############################################################################

print("Attempting to load .env from CWD…")
load_dotenv(override=True)

DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_LANGGRAPH = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

@cl.data_layer
def get_data_layer():
    conninfo = (
        "postgresql+asyncpg://"
        f"{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    )
    print("[DEBUG CL DATA_LAYER] Initialising SQLAlchemyDataLayer…")
    return SQLAlchemyDataLayer(conninfo=conninfo, storage_provider=None)

###############################################################################
# 3. OAuth callback
###############################################################################

@cl.oauth_callback
def oauth_callback(
    provider_id: str,
    token: str,
    raw_user_data: Dict[str, str],
    default_user: cl.User,
) -> Optional[cl.User]:
    print(f"OAuth callback for provider: {provider_id}")
    return default_user

###############################################################################
# 4. Helpers
###############################################################################

def _parse_tool_content(content: Any) -> Dict[str, Any]:
    if isinstance(content, str):
        try: return json.loads(content)
        except json.JSONDecodeError: return {"raw_content": content, "error": "Not valid JSON"}
    if isinstance(content, dict): return content
    return {"raw_content": str(content), "error": "Unknown content type"}

def serialise_state(state: AgentState) -> dict:
    """Make AgentState printable (messages → small dicts)."""
    def _msg_to_dict(m):
        if isinstance(m, (HumanMessage, AIMessage, ToolMessage)):
            return {
                "type": m.__class__.__name__,
                "id": getattr(m, "id", None),
                "content": m.content if isinstance(m.content, str) else "<complex>",
            }
        return str(m)

    # Create a copy of the state to avoid modifying the original
    serialized = dict(state)
    
    # Handle messages specially
    if "messages" in serialized:
        serialized["messages"] = [_msg_to_dict(x) for x in serialized["messages"]]
    
    # Ensure output_image_paths is preserved
    if "output_image_paths" in serialized:
        serialized["output_image_paths"] = list(serialized["output_image_paths"])
    
    return serialized

###############################################################################
# 5. LangGraph initialisation helper 
###############################################################################

async def initialize_langgraph_components(thread_id: str, partner_name: str):
    """Create/restore checkpointer + agent + state for the given thread."""
    print(f"[DEBUG LG] Initialising components for thread_id={thread_id}")

    # 5.1  Checkpointer context manager
    checkpointer_cm = AsyncPostgresSaver.from_conn_string(DB_URI_LANGGRAPH)
    try:
        cp_instance = await checkpointer_cm.__aenter__()
        await cp_instance.setup()
    except Exception as exc:
        print(f"[ERROR LG] Checkpointer setup failed: {exc}")
        cp_instance = None

    cl.user_session.set("lg_checkpointer_cm", checkpointer_cm)
    cl.user_session.set("lg_checkpointer_instance", cp_instance)

    # 5.2  Agent
    if cp_instance:
        lg_agent = create_agent(checkpointer=cp_instance, partner=partner_name) 
        cl.user_session.set("lg_agent", lg_agent)
    else:
        cl.user_session.set("lg_agent", None)

    # 5.3  Agent state
    if cp_instance:
        cfg = RunnableConfig(configurable={"thread_id": thread_id})
        try:
            persisted = await cp_instance.aget(cfg) or {}
        except Exception:
            persisted = {}
    else:
        persisted = {}

    if persisted:
        # rebuild messages list into proper objects
        rebuilt: list[Any] = []
        for md in persisted.get("messages", []):
            if isinstance(md, (HumanMessage, AIMessage, ToolMessage)):
                rebuilt.append(md)
            elif isinstance(md, dict):
                typ = md.get("type", "").lower()
                if "human" in typ:
                    rebuilt.append(HumanMessage(**md))
                elif "ai" in typ:
                    rebuilt.append(AIMessage(**md))
                elif "tool" in typ:
                    rebuilt.append(ToolMessage(**md))
        curr_state: AgentState = {
            **persisted,
            "messages": rebuilt,
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
        }
    else:
        curr_state = {
            "messages": [],
            "remaining_steps": 25,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
            "partner_config": {},
            "summary": "",
            "id_last_summary": None,
        }

    cl.user_session.set("lg_agent_state", curr_state)
    cl.user_session.set("thread_id", thread_id)
    cl.user_session.set("langgraph_initialized_for_thread", bool(cp_instance))

###############################################################################
# 6. Chat‑lifecycle callbacks
###############################################################################

@cl.on_chat_start
async def on_chat_start():
    pn = os.getenv("DEFAULT_PARTNER", "oksigen")
    print(f"[DEBUG CL] on_chat_start: partner_name={pn}")
    cl.user_session.set("partner_name", pn)
    cl.user_session.set("langgraph_initialized_for_thread", False)
    cl.user_session.set("displayed_plot_filenames", set())  # Initialize empty set for tracking displayed plots
    await cl.Message(content=f"Agent initialisé (Partenaire {pn}). Dites‑moi …").send()

@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    tid = thread["id"]
    pn = thread.get("metadata", {}).get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
    await initialize_langgraph_components(tid, pn)
    st = cl.user_session.get("lg_agent_state", {"messages": []})
    cl.user_session.set("displayed_plot_filenames", set())  # Reset displayed plots set on resume
    await cl.Message(
        content=f"Conversation reprise (Partenaire {pn}). {len(st.get('messages', []))} messages enregistrés."
    ).send()

###############################################################################
# 7. Main message handler constants
###############################################################################

# Keys used to identify plots in tool payloads
IMAGE_KEYS = ("output_image_paths", "plots", "IMAGES tool", "IMAGES")

# Tool name mapping for better display
TOOL_DISPLAY_NAMES = {
    "get_data_description": "📊 Analyzing Data Structure",
    "generic_parser": "🔍 Parsing Data",
    "python_repl": "🐍 Executing Python Code",
    "plot_tool": "📈 Creating Visualization",
    "save_plot": "💾 Saving Plot",
}

def get_tool_display_name(tool_name: str) -> str:
    """Get a user-friendly display name for a tool."""
    return TOOL_DISPLAY_NAMES.get(tool_name, f"🔧 {tool_name}")

###############################################################################
# 8. Main message handler
###############################################################################

@cl.on_message
async def on_message(msg_event: cl.Message):
    active_thread_id = cl.context.session.thread_id
    if not active_thread_id:
        print(f"[CRITICAL CL] cl.context.session.thread_id is None in on_message!")
        await cl.Message(content="Erreur critique: Impossible d'identifier la session.").send()
        cl.user_session.set("langgraph_initialized_for_thread", False)
        return

    # Get the set of already displayed plots for this session
    displayed_plots_session = cl.user_session.get("displayed_plot_filenames", set())

    if not cl.user_session.get("langgraph_initialized_for_thread") or \
       cl.user_session.get("thread_id") != active_thread_id:
        print(f"[DEBUG CL] Initializing/Re-syncing LangGraph for thread_id: {active_thread_id}")
        partner_name_for_init = cl.user_session.get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
        await initialize_langgraph_components(active_thread_id, partner_name_for_init)

    lg_agent = cl.user_session.get("lg_agent")
    lg_agent_state: Optional[AgentState] = cl.user_session.get("lg_agent_state")
    partner_name = cl.user_session.get("partner_name")

    print(f"\n[DEBUG CL] === Turn Start for Thread: {active_thread_id}, User Msg ID: {msg_event.id} ===")

    sandbox_client: Optional[SandboxClient] = cl.user_session.get("sandbox_client")
    if not sandbox_client:
        try:
            sandbox_client = SandboxClient()
            cl.user_session.set("sandbox_client", sandbox_client)
            print("[DEBUG CL] SandboxClient instantiated.")
        except Exception as e:
            print(f"[ERROR CL] Failed to instantiate SandboxClient: {e}")
            await cl.Message(content="Erreur de configuration du client Sandbox.").send()
            return

    if not lg_agent or not lg_agent_state or not partner_name:
        await cl.Message(content="Erreur: Agent non initialisé. Veuillez rafraîchir.").send()
        print(f"[ERROR CL] Crucial LangGraph components missing for thread {active_thread_id}.")
        return

    human_message_obj = HumanMessage(content=msg_event.content, id=str(uuid.uuid4()))
    current_messages_for_input = list(lg_agent_state.get("messages", []))
    messages_for_lg_agent_input = current_messages_for_input + [human_message_obj]

    config_for_run = RunnableConfig(
        configurable={"thread_id": active_thread_id, "session_id": active_thread_id, "partner": partner_name}
    )

    assistant_final_msg = cl.Message(content="", author="Assistant")

    new_plot_elements_this_turn: List[cl.Plotly] = []
    processed_plot_filenames_this_turn = set()
    tool_steps: Dict[str, cl.Step] = {}
    thinking_step_shown = False

    assistant_msg_sent = False
    accumulated_content = ""

    final_agent_state_dict: Optional[dict] = None
    messages_from_current_run: List = []

    try:
        async for chunk in lg_agent.astream(
            {"messages": messages_for_lg_agent_input},
            config=config_for_run,
            stream_mode="updates"
        ):
            print(f"[DEBUG STREAM CHUNK] {chunk}")

            if "tools" in chunk:
                node_state = chunk["tools"]
                final_agent_state_dict = node_state
                if "messages" in node_state:
                    for msg in node_state["messages"]:
                        if isinstance(msg, ToolMessage) and msg not in messages_from_current_run:
                            messages_from_current_run.append(msg)
                            tool_call_id = msg.tool_call_id
                            if tool_call_id in tool_steps:
                                tool_step = tool_steps[tool_call_id]
                                tool_content = _parse_tool_content(msg.content)
                                if isinstance(tool_content, dict) and "error" in tool_content:
                                    tool_step.output = f"❌ Error: {tool_content.get('error', 'Unknown error')}"
                                    tool_step.is_error = True
                                else:
                                    output_summary = str(tool_content)[:300] + "..." if len(str(tool_content)) > 300 else str(tool_content)
                                    tool_step.output = f"✅ {output_summary}"
                                await tool_step.update()

                if "output_image_paths" in node_state:
                    plot_step = cl.Step(name="📊 Generating Visualizations", type="tool")
                    await plot_step.send()
                    for plot_path in node_state["output_image_paths"]:
                        if plot_path in displayed_plots_session or plot_path in processed_plot_filenames_this_turn:
                            continue
                        try:
                            pickle_bytes = await sandbox_client.download_plot(session_id=active_thread_id, plot_name=plot_path)
                            if pickle_bytes:
                                fig_obj = pickle.loads(pickle_bytes)
                                fig_obj = apply_company_style(fig_obj)
                                plot_elem = cl.Plotly(name=os.path.basename(plot_path).rsplit(".", 1)[0], figure=fig_obj, display="inline", size="large")
                                new_plot_elements_this_turn.append(plot_elem)
                                displayed_plots_session.add(plot_path)
                                processed_plot_filenames_this_turn.add(plot_path)
                                print(f"[DEBUG PLOT] Added new plot: {plot_path}")
                        except Exception as e:
                            print(f"[ERROR PLOT] Error processing plot {plot_path}: {e}")
                            if plot_step:
                                plot_step.output = f"❌ Error generating visualization: {str(e)}"
                                plot_step.is_error = True
                    if plot_step:
                        plot_step.output = f"✅ Generated {len(new_plot_elements_this_turn)} visualization(s)"
                        await plot_step.update()

            elif "agent" in chunk:
                node_state = chunk["agent"]
                final_agent_state_dict = node_state
                if "messages" in node_state and node_state["messages"]:
                    last_message = node_state["messages"][-1]
                    if last_message not in messages_from_current_run:
                        messages_from_current_run.append(last_message)

                    if isinstance(last_message, AIMessage) and last_message.tool_calls and not thinking_step_shown:
                        thinking_step = cl.Step(name="🤔 Agent Planning", type="llm")
                        await thinking_step.send()
                        for tool_call in last_message.tool_calls:
                            if tool_call["id"] not in tool_steps:
                                # ## FIX 2: Correctly create the step and then set its input.
                                # 1. Create the step without the 'input' argument.
                                tool_step = cl.Step(name=get_tool_display_name(tool_call["name"]), type="tool")
                                # 2. Set the input property on the created step object.
                                tool_step.input = json.dumps(tool_call.get("args", {}), indent=2)
                                # 3. Send the step to the UI.
                                await tool_step.send()
                                # 4. Store it for later reference (to add the output).
                                tool_steps[tool_call["id"]] = tool_step
                        tool_names = [tc["name"] for tc in last_message.tool_calls]
                        thinking_step.output = f"Planning to use: {', '.join(get_tool_display_name(name) for name in tool_names)}"
                        await thinking_step.update()
                        thinking_step_shown = True

                    if isinstance(last_message.content, str) and last_message.content:
                        new_full_content = last_message.content
                        if new_full_content != accumulated_content:
                            if not assistant_msg_sent:
                                await assistant_final_msg.send()
                                assistant_msg_sent = True
                            
                            new_tokens = new_full_content[len(accumulated_content):]
                            
                            if new_tokens:
                                await assistant_final_msg.stream_token(new_tokens)
                            
                            accumulated_content = new_full_content

        if new_plot_elements_this_turn:
            if not assistant_msg_sent:
                await assistant_final_msg.send()
                assistant_msg_sent = True
            assistant_final_msg.elements.extend(new_plot_elements_this_turn)

        if assistant_msg_sent:
            assistant_final_msg.content = accumulated_content
            await assistant_final_msg.update()
        else:
            assistant_final_msg.content = "Traitement terminé. Aucune réponse textuelle générée."
            await assistant_final_msg.send()

        cl.user_session.set("displayed_plot_filenames", displayed_plots_session)

        if lg_agent_state is not None:
            lg_state_msgs = lg_agent_state.get("messages", []) + ensure_message_ids(messages_from_current_run)
            lg_agent_state["messages"], _ = remove_duplicate_messages(lg_state_msgs)
            if final_agent_state_dict:
                if "output_image_paths" in final_agent_state_dict:
                    lg_agent_state["output_image_paths"] = final_agent_state_dict["output_image_paths"]
            cl.user_session.set("lg_agent_state", lg_agent_state)
            print("[DEBUG LG STATE]", pprint.pformat(serialise_state(lg_agent_state), width=100, compact=True))

    except Exception as e_stream:
        print(f"[ERROR CL STREAM] Exception during agent stream: {e_stream}")
        # This will now work correctly because traceback is imported.
        traceback.print_exc(file=sys.stdout)
        await cl.Message(content=f"Une erreur est survenue pendant le traitement: {e_stream}").send()

    finally:
        if assistant_msg_sent and not assistant_final_msg.is_sent():
             await assistant_final_msg.update()
        elif not assistant_msg_sent:
             await cl.Message(content="Traitement terminé.").send()

        print(f"[DEBUG CL] === Turn End for Thread: {active_thread_id} ===\n")



