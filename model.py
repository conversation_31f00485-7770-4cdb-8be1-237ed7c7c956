# model.py
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from langchain_google_genai import ChatGoogleGenerativeAI
from tools import complete_python_task,data_retriever
import os
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pathlib import Path
import os
from dotenv import load_dotenv
from langchain_litellm import ChatLiteLLM



# 1️⃣ Locate & load the .env file (defaults to the first .env it finds upward)
# Load environment variables

load_dotenv()

# Check for required keys

# Set them explicitly if needed (optional since already loaded)
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")



# Default prompt to use if partner is not found
DEFAULT_PROMPT = """
**Role**
  You are a professional data analyst and copilot for an energy monitoring company "Energisme", helping non-technical users retrieve, understand, analyze, and visualize their data. You interact with and guide consumers to achieve their goals by proposing solutions and suggestions based on their objectives and energy monitoring needs. Your internal checks on data structure (like column names) should remain hidden from the user. **You aim to present findings not just accurately, but also in visually compelling and insightful ways.**

IMPORTANT: All datasets retrieved are already loaded as variables in your environment. You should use these variables directly in your code and analysis. Do NOT reload datasets from disk or call pd.read_csv for them.

**Capabilities**
- data_retriever(description: str): Retrieves a dataset based on a description (the user request). When data is needed to fulfill a user request but isn't available in your current environment, you must automatically retrieve it without asking for user approval. 

# Data Request Instructions

When composing your data description, please include the following crucial details:

## 1. **Analysis Objective:**
Clearly state your primary goal. For example:
- "analyze and compare annual energy consumption trends"
- "calculate consumption per area for selected sites"
- "examine load curves during peak and off-peak periods"
- "analyze electricity costs including TVA and commitment compliance"
- "plot the power consumption of the last year"
- "plot the Electric/Energy consumption of the last year"

## 2. **Time Frame and Comparison Requirements:**
Specify the exact period you wish to analyze by providing:
- A start date and an end date in the format YYYY-MM-DDTHH:mm
- If you need data for multiple time frames (e.g., separate years or comparison periods), list each required period. For instance, if you require data for 2024 and 2025, include these in the comparison_periods

## 3. **Site Selection and Scope:**
Indicate whether you want data for all sites or only specific ones. If only certain sites are needed, provide their specific site IDs/codes. If all sites are to be included, simply state "all." For exclusions or customized selections, explain which sites to include by using the appropriate convention (e.g., using a "-" to denote exclusions).

## 4. **Grouping and Aggregation Requirements:**
Describe how you want the data grouped or aggregated. For instance:
- Do you want the data grouped by "site" (to get site-level metrics) or by "date" (to analyze trends over time)?
- If grouping by date, specify the desired time resolution (e.g., daily, weekly, monthly, or yearly)
- If grouping by site, ensure you list the site codes as needed
- **Important:** Choose one grouping method - you cannot choose both site and date grouping simultaneously

## **Special Instructions for Specific Data Types:**

### Energy-Related Requests:
- When users ask about **electricity costs**, **TVA (taxes)**, **gas emissions**, or **commitment-related indicators**, ask the data_retriever to obtain these specific data types
- When users ask about "energy," do not automatically infer they mean "electricity" - put exactly what the user requested in your data request
- If you need clarification about the specific type of energy data required, ask the user what specific data they need

### Data Retrieval Protocol:
- Always request the specific data type mentioned by the user
- If ambiguous, ask for clarification rather than making assumptions
- Ensure your data request matches the user's exact terminology and requirements


- complete_python_task(python_code: str): Executes Python code using pandas, plotly, and sklearn.

   Avoid calling tools that are not listed above.

**HANDLING DATA RETRIEVER REQUESTS:**
- If the data retriever indicates it needs additional information (status: "info_needed"), ask the user for the specific information mentioned in "needed_infos" if you do not already have it. If you have the information, provide it directly in your next data retrieval request.
- When you receive the additional information from the user, include it in your next data retrieval request (which should contain the previous request plus the new information).
- Be specific about what information is needed and why it's required for data retrieval.
- Always provide context about how the additional information will help improve the data retrieval.


**DOMAIN-RESTRICTION RULE:**
  • **Answer exclusively questions related to energy or energy-consumption topics.**  
  • If a user asks about anything outside this scope (sport, HR, general weather, etc.), reply with a brief apology and a single-sentence refusal:  
    "I'm sorry, but I can only help with questions related to energy and consumption data."  
  • Do **not** provide partial answers, general advice, or redirections on unrelated topics.

**Core Directives**
  - **Prioritize Accurate Goal Achievement:** Address every specified constraint (e.g., "consumption by area," exact time frames, and comparison types) while keeping responses concise. Always clarify whether the user needs power (kW) or energy (kWh) metrics.
  - **Plan Before Acting:** Briefly outline your goal, the conceptual data required, and the main steps in as few words as necessary.
  - **Validate Relentlessly (Internal Process):**
      - **Infer, Then Verify:** Extract as much necessary detail from the user's request without requesting raw data details.
      - **Pre-Retrieval Check:** Before retrieving data, inspect the description of available data to see if it meets the requirements. If none is suitable, retrieve new data using data_retriever without asking the user for permission.
      - **Post-Load Inspection (Internal):** Once data is retrieved, inspect it immediately with Python code and document your findings internally.
      - **Confirm Capability (External Communication):** Verify that the data has all necessary information to meet the user's objective, and communicate only the key points.
      - **Report Limitations Briefly:** If data is insufficient, state limitations succinctly (e.g., "the data may not support a detailed analysis") without mentioning specific columns. Offer alternative approaches if needed.
      - **Never reveal specific data column names or internal inspection methods to the user.**
  - **Maintain Goal Focus:**
      - **Reiterate the User's Objective:** Restate the overall goal briefly.
      - **Align Actions:** Ensure every step directly addresses the goal using the validated data, and do not repeat already completed steps.
      - **Justify Deviations Briefly:** If a deviation from the plan is necessary, explain succinctly why and confirm with the user before proceeding.
  - **Leverage Prior Work:** Use outcomes and validated data from previous steps instead of repeating analysis.
  - **Collaborate Effectively:**
      - **Guide Clearly:** Propose next steps, give clear and concise explanations of results, and suggest follow-up analyses.
      - **Propose Engaging Visualizations:** When visualizing data, select clear, impactful chart types (e.g., heatmaps, bubble charts, treemaps) and briefly explain why they are optimal.
      - **Confirm Understanding:** Verify your understanding of the goal and plan with the user in a concise manner.
  - **Use Tools Appropriately:**
      - Begin with internal inspections using persistent variables, and only invoke data_retriever if it is confirmed that available data cannot fully achieve the task.
      - Always retrieve new data when necessary without asking for user permission.
      - **Proactively retrieve cost, TVA, and commitment data when users ask about financial aspects.**

**Code Guidelines**
  - The data is already loaded, if no load it.
  - **Trust Your Inspections:** Base decisions on previously validated data. Use the 'thought' field for technical details while keeping them hidden from the user.
  - VARIABLES persist between runs.
  - Use print() to display outputs as needed.
  - Only use libraries: pandas, sklearn, plotly.
  - Never create or use dummy/sample data unless explicitly asked.

**Plotting Guidelines**
  - **Aim for Excellence:** Always use Plotly to create visually compelling, insightful, and creatively designed charts.
  - **Always Use R Theme:** Configure all Plotly visualizations with the R theme (template='ggplot2') for sophisticated and professional-looking charts.
  - **Create Visually Astonishing Plots:** Generate visually stunning and aesthetically pleasing visualizations by:
      - Using carefully selected color palettes that are both attractive and meaningful
      - Including appropriate annotations, labels, and hover information
      - Implementing thoughtful layout designs with proper spacing, fonts, and background elements
      - Adding interactive elements that enhance user engagement without sacrificing clarity
      - Optimizing chart dimensions and proportions for maximum visual impact
  - **Balance Creativity and Clarity:** Even innovative visualizations must remain easy for non-technical users to interpret.
  - **Purposeful Design:** Choose visualization types deliberately to highlight the key insights relevant to the user's goals.
  - **Implementation:**
      - Store all Plotly figures in a list called plotly_figures.
      - Append each figure with: plotly_figures.append(fig).
      - Ensure visuals derive from validated data from previous inspections.
      - Briefly describe the contents of each plot (e.g., "This chart compares site consumption trends over the selected period") in concise language.
      - Do not use fig.show().

"""


def get_prompt_for_partner():
    """Get the appropriate prompt for the specified partner"""
    from datetime import datetime
    current_date = datetime.now().date()

    # Get the partner-specific prompt or use the default
    prompt_template = DEFAULT_PROMPT

    # Add the current date
    prompt = f"{prompt_template}\n\nFor info:\n    - Today is: {current_date}"

    return prompt

# Get the current date for default prompt
from datetime import datetime
current_date = datetime.now().date()

# Default prompt (for backward compatibility)
prompt = get_prompt_for_partner()
#  - **Never Use Dummy or Generated Data:** Always work with actual retrieved data. Never generate dummy or sample data as a substitute for real data unless explicitly requested by the user. If necessary data is not available, explain the limitation without creating placeholder data.
# - **Only Visualize Real Data:** Never create visualizations based on dummy, generated, or placeholder data unless specifically requested by the user.

chat_template = ChatPromptTemplate.from_messages([
    ("system", prompt),
    ("placeholder", "{messages}"),
])

######################################## Main model ##############################################

# OpenAI
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
MAIN_MODEL = "gpt-4.1"
SUMMARY_MODEL = "gpt-4.1-nano"
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")  # Replace with your actual key
OPENAI_API_VERSION = "2024-12-01-preview"

#DeepsEEK
AZURE_DEEPSEEK_API_KEY=os.getenv("AZURE_DEEPSEEK_API_KEY")
AZURE_DEEPSEEK_ENDPOINT=os.getenv("AZURE_DEEPSEEK_ENDPOINT")

def get_llm_model():
    """
    Initializes a chain of LLM models with a specified fallback order.

    The fallback order is: Azure OpenAI -> Azure DeepSeek -> OpenAI.

    If a model fails to respond (e.g., due to API errors, rate limits), 
    the request is automatically passed to the next model in the chain.

    Model names can be customized via a `model_config.json` file.
    
    Returns:
        A LangChain runnable (ChatModel) with fallbacks configured, or raises
        a RuntimeError if no models could be initialized.
    """
    # Default model names based on your provided snippets
    model_configs = {
        "openai": "gpt-4.1",
    }

    # --- Instantiate all potential LLMs ---
    # We set max_retries=0 to ensure that errors are not handled internally
    # by the model's client, allowing the fallback logic to trigger.
    
    initialized_llms = []
    llm_info = []  # Store tuples of (llm, provider_name, model_name)
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        model = AzureChatOpenAI(
            azure_deployment=MAIN_MODEL,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=OPENAI_API_VERSION,
        )
        initialized_llms.append(model)
        llm_info.append(("Azure OpenAI", "gpt-4.1"))
        print(f"✅ Initialized Azure OpenAI: gpt-4.1")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure OpenAI model. It will be skipped. Error: {e}")

        # 2. DeepSeek (Secondary)
    try:
                # Option 2: Create a custom LangChain wrapper
        from langchain.chat_models.base import BaseChatModel
        from langchain.schema import ChatResult, ChatGeneration, AIMessage
        from azure.ai.inference import ChatCompletionsClient
        from azure.ai.inference.models import UserMessage, SystemMessage
        from azure.core.credentials import AzureKeyCredential
        
        class AzureAIInferenceChatModel(BaseChatModel):
            endpoint: str = ""
            api_key: str = ""
            model_name: str = ""

            def __init__(self, endpoint: str, api_key: str, model_name: str, **kwargs):
                super().__init__(endpoint=endpoint, api_key=api_key, model_name=model_name, **kwargs)

            def _generate(self, messages, stop=None, **kwargs):
                client = ChatCompletionsClient(
                    endpoint=self.endpoint,
                    credential=AzureKeyCredential(self.api_key),
                    api_version="2024-05-01-preview"
                )
                
                # Convert LangChain messages to Azure AI format
                azure_messages = []
                for msg in messages:
                    if msg.type == "system":
                        azure_messages.append(SystemMessage(content=msg.content))
                    else:
                        azure_messages.append(UserMessage(content=msg.content))
                
                response = client.complete(
                    messages=azure_messages,
                    model=self.model_name,
                    **kwargs
                )
                
                return ChatResult(
                    generations=[ChatGeneration(
                        message=AIMessage(content=response.choices[0].message.content)
                    )]
                )
            
            @property
            def _llm_type(self):
                return "azure-ai-inference"
        
        deepseek_llm = AzureAIInferenceChatModel(
            endpoint=AZURE_DEEPSEEK_ENDPOINT,
            api_key=AZURE_DEEPSEEK_API_KEY,
            model_name="DeepSeek-V3-0324"
        )
        
        initialized_llms.append(deepseek_llm)
        llm_info.append(("DeepSeek", model_configs["deepseek"]))
        print(f"✅ Initialized DeepSeek: {model_configs['deepseek']}")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure DeepSeek model. It will be skipped. Error: {e}")

    # 3. OpenAI 
    try:
        openai_llm = ChatOpenAI(
            model=model_configs["openai"],
            temperature=0,
            max_retries=2 
        )
        initialized_llms.append(openai_llm)
        llm_info.append(("OpenAI", model_configs["openai"]))
        print(f"✅ Initialized OpenAI: {model_configs['openai']}")
    except Exception as e:
        print(f"⚠️  Could not initialize OpenAI model. It will be skipped. Error: {e}")

    

    # --- Build the fallback chain ---
    if not initialized_llms:
        raise RuntimeError("No LLM providers could be initialized. Please check your API keys (e.g., OPENAI_API_KEY) and configurations.")

    # The first successfully initialized LLM is the primary
    primary_llm = initialized_llms[0]
    primary_info = llm_info[0]
    
    # The rest are the fallbacks
    fallback_llms = initialized_llms[1:]
    fallback_info = llm_info[1:]

    if not fallback_llms:
        print("Note: Only one LLM was initialized. No fallbacks are configured.")
        return primary_llm

    print(f"\n🚀 Using '{primary_info[0]} ({primary_info[1]})' as primary model.")
    print("Fallback chain:")
    for i, (provider, model) in enumerate(fallback_info):
        print(f"  {i+1}. {provider} ({model})")
        
    # Chain the primary model with the list of fallbacks
    model_with_fallbacks = primary_llm.with_fallbacks(fallback_llms)

    return model_with_fallbacks

model = get_llm_model()

################################# Summary Model #############################################

def get_llm_model_summary():
    """
    Initializes a chain of LLM models with a specified fallback order.

    The fallback order is: Azure OpenAI -> Azure DeepSeek -> OpenAI.

    If a model fails to respond (e.g., due to API errors, rate limits), 
    the request is automatically passed to the next model in the chain.

    Model names can be customized via a `model_config.json` file.
    
    Returns:
        A LangChain runnable (ChatModel) with fallbacks configured, or raises
        a RuntimeError if no models could be initialized.
    """
    # Default model names based on your provided snippets
    model_configs = {
        "openai": "gpt-4.1-nano",
    }

    # --- Instantiate all potential LLMs ---
    # We set max_retries=0 to ensure that errors are not handled internally
    # by the model's client, allowing the fallback logic to trigger.
    
    initialized_llms = []
    llm_info = []  # Store tuples of (llm, provider_name, model_name)
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        model = AzureChatOpenAI(
            azure_deployment=SUMMARY_MODEL,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=OPENAI_API_VERSION,

        )
        initialized_llms.append(model)
        llm_info.append(("Azure OpenAI", "gpt-4.1-nano"))
        print(f"✅ Initialized Azure OpenAI: gpt-4.1-nano")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure OpenAI model. It will be skipped. Error: {e}")

        # 2. DeepSeek (Secondary)
    try:
                # Option 2: Create a custom LangChain wrapper
        from langchain.chat_models.base import BaseChatModel
        from langchain.schema import ChatResult, ChatGeneration, AIMessage
        from azure.ai.inference import ChatCompletionsClient
        from azure.ai.inference.models import UserMessage, SystemMessage
        from azure.core.credentials import AzureKeyCredential
        
        class AzureAIInferenceChatModel(BaseChatModel):
            endpoint: str = ""
            api_key: str = ""
            model_name: str = ""

            def __init__(self, endpoint: str, api_key: str, model_name: str, **kwargs):
                super().__init__(endpoint=endpoint, api_key=api_key, model_name=model_name, **kwargs)

            def _generate(self, messages, stop=None, **kwargs):
                client = ChatCompletionsClient(
                    endpoint=self.endpoint,
                    credential=AzureKeyCredential(self.api_key),
                    api_version="2024-05-01-preview"
                )
                
                # Convert LangChain messages to Azure AI format
                azure_messages = []
                for msg in messages:
                    if msg.type == "system":
                        azure_messages.append(SystemMessage(content=msg.content))
                    else:
                        azure_messages.append(UserMessage(content=msg.content))
                
                response = client.complete(
                    messages=azure_messages,
                    model=self.model_name,
                    **kwargs
                )
                
                return ChatResult(
                    generations=[ChatGeneration(
                        message=AIMessage(content=response.choices[0].message.content)
                    )]
                )
            
            @property
            def _llm_type(self):
                return "azure-ai-inference"
        
        deepseek_llm = AzureAIInferenceChatModel(
            endpoint=AZURE_DEEPSEEK_ENDPOINT,
            api_key=AZURE_DEEPSEEK_API_KEY,
            model_name="DeepSeek-V3-0324"
        )
        
        initialized_llms.append(deepseek_llm)
        llm_info.append(("DeepSeek", model_configs["deepseek"]))
        print(f"✅ Initialized DeepSeek: {model_configs['deepseek']}")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure DeepSeek model. It will be skipped. Error: {e}")   
    # 3. OpenAI 
    try:
        openai_llm = ChatOpenAI(
            model=model_configs["openai"],
            temperature=0,
            max_retries=2 
        )
        initialized_llms.append(openai_llm)
        llm_info.append(("OpenAI", model_configs["openai"]))
        print(f"✅ Initialized OpenAI: {model_configs['openai']}")
    except Exception as e:
        print(f"⚠️  Could not initialize OpenAI model. It will be skipped. Error: {e}")


    # --- Build the fallback chain ---
    if not initialized_llms:
        raise RuntimeError("No LLM providers could be initialized. Please check your API keys (e.g., OPENAI_API_KEY) and configurations.")

    # The first successfully initialized LLM is the primary
    primary_llm = initialized_llms[0]
    primary_info = llm_info[0]
    
    # The rest are the fallbacks
    fallback_llms = initialized_llms[1:]
    fallback_info = llm_info[1:]

    if not fallback_llms:
        print("Note: Only one LLM was initialized. No fallbacks are configured.")
        return primary_llm

    print(f"\n🚀 Using '{primary_info[0]} ({primary_info[1]})' as primary model.")
    print("Fallback chain:")
    for i, (provider, model) in enumerate(fallback_info):
        print(f"  {i+1}. {provider} ({model})")
        
    # Chain the primary model with the list of fallbacks
    model_with_fallbacks = primary_llm.with_fallbacks(fallback_llms)

    return model_with_fallbacks

summary_model = get_llm_model_summary()


