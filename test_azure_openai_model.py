"""
Multiple solutions for LangChain Azure OpenAI connection issues
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Your working configuration
ENDPOINT = "https://ai-agent-energisme.services.ai.azure.com/models"
DEPLOYMENT = "DeepSeek-V3-0324"
API_KEY = os.getenv("AZURE_DEEPSEEK_API_KEY")  # Replace with your actual key
API_VERSION = "2024-05-01-preview"


def solution_1_basic_langchain():
    """Fixed solution using the correct LangChain approach for Azure AI"""
    print("🔧 Solution 1: Fixed LangChain setup")
    
    try:
        # Option 1: Use the Azure AI Inference integration if available
        from langchain_community.chat_models import AzureMLChatOnlineEndpoint
        from azure.core.credentials import AzureKeyCredential
        
        llm = AzureMLChatOnlineEndpoint(
            endpoint_url=ENDPOINT,
            endpoint_api_key=API_KEY,
            content_formatter=None,  # Use default formatter
            model_kwargs={
                "model": "DeepSeek-V3-0324",

            }
        )
        
        response = llm.invoke('Tell me a joke and include some emojis')
        print(f"✅ Success: {response.content[:100]}...")
        print("This one worked")
        return True
        
    except ImportError:
        # Option 2: Create a custom LangChain wrapper
        from langchain.chat_models.base import BaseChatModel
        from langchain.schema import ChatResult, ChatGeneration, AIMessage
        from azure.ai.inference import ChatCompletionsClient
        from azure.ai.inference.models import UserMessage
        from azure.core.credentials import AzureKeyCredential
        
        class AzureAIInferenceChatModel(BaseChatModel):
            endpoint: str
            api_key: str
            model_name: str
            
            def _generate(self, messages, stop=None, **kwargs):
                client = ChatCompletionsClient(
                    endpoint=self.endpoint,
                    credential=AzureKeyCredential(self.api_key),
                    api_version="2024-05-01-preview"
                )
                
                # Convert LangChain messages to Azure AI format
                azure_messages = []
                for msg in messages:
                    if msg.type == "system":
                        azure_messages.append(SystemMessage(content=msg.content))
                    else:
                        azure_messages.append(UserMessage(content=msg.content))
                
                response = client.complete(
                    messages=azure_messages,
                    model=self.model_name,
                    **kwargs
                )
                
                return ChatResult(
                    generations=[ChatGeneration(
                        message=AIMessage(content=response.choices[0].message.content)
                    )]
                )
            
            @property
            def _llm_type(self):
                return "azure-ai-inference"
        
        llm = AzureAIInferenceChatModel(
            endpoint=ENDPOINT,
            api_key=API_KEY,
            model_name="DeepSeek-V3-0324"
        )
        
        response = llm.invoke('Tell me a joke and include some emojis')
        print(f"✅ Success: {response.content[:100]}...")
        print("This second one worked")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def solution_2_environment_variables():
    """Solution 2: Use environment variables (LangChain's preferred method)"""
    print("\n🔧 Solution 2: Using environment variables")
    
    try:
        # Set environment variables that LangChain looks for
        os.environ["AZURE_OPENAI_API_KEY"] = API_KEY
        os.environ["AZURE_OPENAI_ENDPOINT"] = ENDPOINT
        os.environ["AZURE_OPENAI_API_VERSION"] = API_VERSION
        os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"] = DEPLOYMENT
        
        from langchain_openai import AzureChatOpenAI
        
        # LangChain should automatically pick up these environment variables
        llm = AzureChatOpenAI(
            azure_deployment=DEPLOYMENT,
            temperature=1.0,
            max_tokens=800,
        )
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def solution_3_different_api_version():
    """Solution 3: Try different API versions"""
    print("\n🔧 Solution 3: Testing different API versions")
    
    api_versions = [
        "2024-08-01-preview",
        "2024-06-01",
        "2024-02-01",
        "2023-12-01-preview",
        "2024-12-01-preview"  # Your current version
    ]
    
    from langchain_openai import AzureChatOpenAI
    
    for version in api_versions:
        try:
            print(f"   Testing API version: {version}")
            
            llm = AzureChatOpenAI(
                azure_deployment=DEPLOYMENT,
                azure_endpoint=ENDPOINT,
                api_key=API_KEY,
                api_version=version,
                temperature=1.0,
                max_tokens=800,
                timeout=30,  # Add timeout
            )
            
            response = llm.invoke("Hello!")
            print(f"✅ Success with {version}: {response.content[:50]}...")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed with {version}: {str(e)[:80]}...")
            continue
    
    return False


def solution_4_explicit_client():
    """Solution 4: Create explicit OpenAI client for LangChain"""
    print("\n🔧 Solution 4: Using explicit OpenAI client")
    
    try:
        from openai import AzureOpenAI
        from langchain_openai import AzureChatOpenAI
        
        # Create the same client that works for you
        azure_client = AzureOpenAI(
            api_version=API_VERSION,
            azure_endpoint=ENDPOINT,
            api_key=API_KEY,
        )
        
        # Pass the client to LangChain
        llm = AzureChatOpenAI(
            azure_deployment=DEPLOYMENT,
            azure_endpoint=ENDPOINT,
            api_key=API_KEY,
            api_version=API_VERSION,
            temperature=1.0,
            max_tokens=800,
            # client=azure_client,  # Some versions support this
        )
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def solution_5_debug_parameters():
    """Solution 5: Debug with verbose parameters"""
    print("\n🔧 Solution 5: Debug with all parameters explicit")
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        # Print all parameters for debugging
        params = {
            "azure_deployment": DEPLOYMENT,
            "azure_endpoint": ENDPOINT,
            "api_key": API_KEY,
            "api_version": API_VERSION,
            "temperature": 1.0,
            "max_tokens": 800,
            "timeout": 60,
            "max_retries": 3,
            "request_timeout": 60,
        }
        
        print("   Parameters:")
        for key, value in params.items():
            if key == "api_key":
                print(f"     {key}: {'*' * 10}...{value[-4:] if value else 'None'}")
            else:
                print(f"     {key}: {value}")
        
        llm = AzureChatOpenAI(**params)
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


def solution_6_alternative_import():
    """Solution 6: Try alternative import method"""
    print("\n🔧 Solution 6: Alternative import method")
    
    try:
        from langchain.chat_models import init_chat_model
        
        llm = init_chat_model(
            "azure-openai:gpt-4.1-nano",  # Format: provider:model
            api_key=API_KEY,
            azure_endpoint=ENDPOINT,
            api_version=API_VERSION,
            temperature=1.0,
            max_tokens=800,
        )
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def main():
    """Test all solutions"""
    print("🔬 LangChain Azure OpenAI Connection Solutions")
    print("=" * 60)
    
    if not API_KEY:
        print("❌ Error: Please set your AZURE_OPENAI_API_KEY in .env file")
        return
    
    solutions = [
        solution_1_basic_langchain,
        solution_2_environment_variables,
        solution_3_different_api_version,
        solution_4_explicit_client,
        solution_5_debug_parameters,
        solution_6_alternative_import,
    ]
    
    for i, solution in enumerate(solutions, 1):
        try:
            if solution():
                print(f"\n🎉 Solution {i} worked! You can use this approach.")
                break
        except ImportError as e:
            print(f"❌ Import error in solution {i}: {e}")
        except Exception as e:
            print(f"❌ Unexpected error in solution {i}: {e}")
    else:
        print("\n💥 All solutions failed. Additional debugging needed.")
        print("\n🔧 Next steps:")
        print("1. Check your LangChain version: pip show langchain-openai")
        print("2. Try updating: pip install --upgrade langchain-openai")
        print("3. Verify your deployment name in Azure Portal")
        print("4. Test with a simpler deployment name (e.g., 'gpt-4')")


if __name__ == "__main__":
    main()